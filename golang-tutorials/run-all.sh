#!/bin/bash

echo "🚀 Running All Go Tutorial Examples"
echo "===================================="

# Array of tutorial directories
tutorials=("day1_basics" "day2_structs_interfaces" "day3_concurrency" "day4_restapi1" "day5_restapi2")

for tutorial in "${tutorials[@]}"; do
    if [ -d "$tutorial" ] && [ -f "$tutorial/run.sh" ]; then
        echo ""
        echo "🎯 Running $tutorial..."
        echo "----------------------------------------"
        cd "$tutorial"
        ./run.sh
        cd ..
        echo ""
        echo "✅ $tutorial completed!"
        echo "========================================"
    else
        echo "⚠️  Skipping $tutorial (not found or no run script)"
    fi
done

echo ""
echo "🎉 All available tutorials completed!"
echo "💡 To run individual tutorials: cd <tutorial_folder> && ./run.sh"
