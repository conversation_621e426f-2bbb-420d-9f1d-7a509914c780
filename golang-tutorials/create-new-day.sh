#!/bin/bash

# Script to create a new daily tutorial from template

if [ $# -ne 2 ]; then
    echo "Usage: $0 <day_number> <topic_name>"
    echo "Example: $0 6 \"Error Handling & Testing\""
    exit 1
fi

DAY_NUMBER=$1
TOPIC_NAME=$2
FOLDER_NAME="day${DAY_NUMBER}_$(echo "$TOPIC_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/_/g' | sed 's/__*/_/g' | sed 's/^_\|_$//g')"

echo "Creating new tutorial: $FOLDER_NAME"

# Create the directory
mkdir -p "$FOLDER_NAME"
mkdir -p "$FOLDER_NAME/solutions"

# Copy template files
cp template/README.md "$FOLDER_NAME/"
cp template/example1.go "$FOLDER_NAME/"
cp template/run.sh "$FOLDER_NAME/"

# Create additional example files
cat > "$FOLDER_NAME/example2.go" << EOF
package main

import "fmt"

// Example 2: [Brief description of what this example demonstrates]
func main() {
	fmt.Println("=== $TOPIC_NAME Example 2 ===")
	
	// TODO: Add your example code here
	// This should demonstrate the second key concept
	
	fmt.Println("Example 2 completed!")
}
EOF

cat > "$FOLDER_NAME/example3.go" << EOF
package main

import "fmt"

// Example 3: [Brief description of what this example demonstrates]
func main() {
	fmt.Println("=== $TOPIC_NAME Example 3 ===")
	
	// TODO: Add your example code here
	// This should demonstrate the third key concept
	
	fmt.Println("Example 3 completed!")
}
EOF

cat > "$FOLDER_NAME/advanced.go" << EOF
package main

import "fmt"

// Advanced Example: Real-world application of $TOPIC_NAME concepts
func main() {
	fmt.Println("=== $TOPIC_NAME Advanced Example ===")
	
	// TODO: Add your advanced example code here
	// This should be a real-world application of the concepts
	
	fmt.Println("Advanced example completed!")
}
EOF

# Update the README with actual day number and topic
sed -i.bak "s/Day X/$DAY_NUMBER/g" "$FOLDER_NAME/README.md"
sed -i.bak "s/\[Topic Name\]/$TOPIC_NAME/g" "$FOLDER_NAME/README.md"
rm "$FOLDER_NAME/README.md.bak"

# Update the run script
sed -i.bak "s/Day X/Day $DAY_NUMBER/g" "$FOLDER_NAME/run.sh"
sed -i.bak "s/\[Topic Name\]/$TOPIC_NAME/g" "$FOLDER_NAME/run.sh"
rm "$FOLDER_NAME/run.sh.bak"

# Make run script executable
chmod +x "$FOLDER_NAME/run.sh"

# Create a basic exercise solution template
cat > "$FOLDER_NAME/solutions/exercise1.go" << EOF
package main

import "fmt"

// Exercise 1 Solution: [Description]
func main() {
	fmt.Println("=== Exercise 1 Solution ===")
	
	// TODO: Implement exercise solution
	
	fmt.Println("Exercise 1 completed!")
}
EOF

echo "✅ Created new tutorial: $FOLDER_NAME"
echo "📝 Files created:"
echo "   - README.md (tutorial documentation)"
echo "   - example1.go, example2.go, example3.go (code examples)"
echo "   - advanced.go (real-world example)"
echo "   - run.sh (script to run all examples)"
echo "   - solutions/exercise1.go (exercise solution template)"
echo ""
echo "🚀 Next steps:"
echo "   1. Edit the README.md to add your tutorial content"
echo "   2. Implement the example code in the .go files"
echo "   3. Add exercises and solutions"
echo "   4. Test with: cd $FOLDER_NAME && ./run.sh"
