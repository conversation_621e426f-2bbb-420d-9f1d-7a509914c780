package main

import "fmt"

// Basic function with parameters and return value
func add(a int, b int) int {
	return a + b
}

// Function with multiple parameters of same type (shorthand)
func multiply(x, y int) int {
	return x * y
}

// Function with multiple return values
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.<PERSON>rrorf("division by zero")
	}
	return a / b, nil
}

// Function with named return values
func rectangle(length, width float64) (area, perimeter float64) {
	area = length * width
	perimeter = 2 * (length + width)
	return // naked return - returns named values
}

// Function that takes no parameters
func greet() string {
	return "Hello from a function!"
}

// Function with variadic parameters (variable number of arguments)
func sum(numbers ...int) int {
	total := 0
	for _, num := range numbers {
		total += num
	}
	return total
}

func main() {
	fmt.Println("=== Go Functions Demo ===")
	
	// Basic function call
	result := add(5, 3)
	fmt.Printf("5 + 3 = %d\n", result)
	
	// Multiple parameter function
	product := multiply(4, 7)
	fmt.Printf("4 * 7 = %d\n", product)
	
	// Function with multiple return values
	quotient, err := divide(10, 3)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("10 / 3 = %.2f\n", quotient)
	}
	
	// Error case
	_, err = divide(10, 0)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	}
	
	// Named return values
	area, perimeter := rectangle(5, 3)
	fmt.Printf("Rectangle (5x3): Area = %.2f, Perimeter = %.2f\n", area, perimeter)
	
	// Function with no parameters
	greeting := greet()
	fmt.Println(greeting)
	
	// Variadic function
	total := sum(1, 2, 3, 4, 5)
	fmt.Printf("Sum of 1,2,3,4,5 = %d\n", total)
	
	// Variadic with slice
	numbers := []int{10, 20, 30}
	total2 := sum(numbers...) // spread operator
	fmt.Printf("Sum of slice [10,20,30] = %d\n", total2)
}
