# Day 1: Go Basics & Tooling

## Goal
Set up your Go environment, understand Go modules, and write your first Go program.

---

## 🚀 Quick Start
Run all examples for this day:
```bash
./run.sh
```

Or run individual examples:
```bash
go run hello.go
go run types.go
go run functions.go
```

---

## 1. Install Go
- Download and install Go from [https://golang.org/dl/](https://golang.org/dl/)
- Verify installation:
  ```sh
  go version
  ```

## 2. Go Modules
- Initialize a new module:
  ```sh
  go mod init example.com/helloworld
  ```
- This creates a `go.mod` file for dependency management.

## 3. Hello World Example
📁 **File: `hello.go`**

The classic first program in Go. This demonstrates:
- Package declaration
- Import statements
- The main function

## 4. Basic Types and Variables
📁 **File: `types.go`**

Learn about Go's basic data types:
- Integers, floats, strings, booleans
- Variable declarations
- Type inference

## 5. Functions
📁 **File: `functions.go`**

Understanding Go functions:
- Function syntax
- Parameters and return values
- Multiple return values

---

## 🎯 Practice Exercises

1. **Exercise 1**: Modify `hello.go` to print your name
2. **Exercise 2**: Create a function that calculates the area of a rectangle
3. **Exercise 3**: Write a program that converts Celsius to Fahrenheit

**Solutions are in the `solutions/` folder**

---

## Summary
- You learned how to set up Go, use modules, and write/run basic programs.
- You practiced with variables, types, and functions.
- Next: Structs, methods, and interfaces.

