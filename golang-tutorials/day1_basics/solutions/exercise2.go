package main

import "fmt"

// Exercise 2 Solution: Function that calculates the area of a rectangle
func rectangleArea(length, width float64) float64 {
	return length * width
}

// Bonus: Function that calculates both area and perimeter
func rectangleCalculations(length, width float64) (area, perimeter float64) {
	area = length * width
	perimeter = 2 * (length + width)
	return
}

func main() {
	fmt.Println("=== Rectangle Calculator ===")
	
	// Test the area function
	length := 5.5
	width := 3.2
	
	area := rectangleArea(length, width)
	fmt.Printf("Rectangle with length %.1f and width %.1f\n", length, width)
	fmt.Printf("Area: %.2f square units\n", area)
	
	// Test the bonus function
	fmt.Println("\n--- Bonus: Complete calculations ---")
	area2, perimeter := rectangleCalculations(length, width)
	fmt.Printf("Area: %.2f square units\n", area2)
	fmt.Printf("Perimeter: %.2f units\n", perimeter)
}
