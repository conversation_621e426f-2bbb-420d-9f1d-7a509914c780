package main

import "fmt"

// Exercise 3 Solution: Convert Celsius to Fahrenheit
func celsiusToFahrenheit(celsius float64) float64 {
	return (celsius * 9/5) + 32
}

// Bonus: Convert Fahrenheit to Celsius
func fahrenheitToCelsius(fahrenheit float64) float64 {
	return (fahrenheit - 32) * 5/9
}

// Bonus: Temperature converter with multiple units
func convertTemperature(temp float64, fromUnit, toUnit string) (float64, error) {
	switch {
	case fromUnit == "C" && toUnit == "F":
		return celsiusToFahrenheit(temp), nil
	case fromUnit == "F" && toUnit == "C":
		return fahrenheitToCelsius(temp), nil
	case fromUnit == toUnit:
		return temp, nil
	default:
		return 0, fmt.Errorf("unsupported conversion from %s to %s", fromUnit, toUnit)
	}
}

func main() {
	fmt.Println("=== Temperature Converter ===")
	
	// Test Celsius to Fahrenheit
	celsius := 25.0
	fahrenheit := celsiusToFahrenheit(celsius)
	fmt.Printf("%.1f°C = %.1f°F\n", celsius, fahrenheit)
	
	// Test Fahrenheit to Celsius
	fahrenheit = 77.0
	celsius = fahrenheitToCelsius(fahrenheit)
	fmt.Printf("%.1f°F = %.1f°C\n", fahrenheit, celsius)
	
	// Test some common temperatures
	fmt.Println("\n--- Common Temperature Conversions ---")
	commonTemps := []float64{0, 100, 32, 212, -40}
	
	for _, temp := range commonTemps {
		f := celsiusToFahrenheit(temp)
		fmt.Printf("%.0f°C = %.1f°F\n", temp, f)
	}
	
	// Test the bonus converter function
	fmt.Println("\n--- Using the Universal Converter ---")
	result, err := convertTemperature(20, "C", "F")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("20°C = %.1f°F\n", result)
	}
}
