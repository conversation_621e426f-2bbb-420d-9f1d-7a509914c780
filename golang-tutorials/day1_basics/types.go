package main

import "fmt"

// Demonstrates Go's basic types and variable declarations
func main() {
	fmt.Println("=== Go Basic Types Demo ===")
	
	// Integer types
	var age int = 25
	var population int64 = 7800000000
	fmt.Printf("Age: %d, World Population: %d\n", age, population)
	
	// Float types
	var price float64 = 19.99
	var discount float32 = 0.15
	fmt.Printf("Price: $%.2f, Discount: %.2f%%\n", price, discount*100)
	
	// String type
	var message string = "Go is awesome!"
	var shortMsg = "Type inference works!" // Go infers the type
	fmt.Printf("Message: %s\n", message)
	fmt.Printf("Short message: %s\n", shortMsg)
	
	// Boolean type
	var isLearning bool = true
	var isExpert = false // Type inference
	fmt.Printf("Learning Go: %t, Expert level: %t\n", isLearning, isExpert)
	
	// Multiple variable declaration
	var (
		firstName = "John"
		lastName  = "Doe"
		userAge   = 30
	)
	fmt.Printf("User: %s %s, Age: %d\n", firstName, lastName, userAge)
	
	// Short variable declaration (only inside functions)
	city := "New York"
	country := "USA"
	fmt.Printf("Location: %s, %s\n", city, country)
	
	// Constants
	const pi = 3.14159
	const greeting = "Hello"
	fmt.Printf("Pi: %.5f, Greeting: %s\n", pi, greeting)
}
