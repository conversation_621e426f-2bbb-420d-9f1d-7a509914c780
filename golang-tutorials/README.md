# Go Programming Tutorials - Daily Learning Path

Welcome to your comprehensive Go programming tutorial series! This repository contains daily tutorials designed to take you from beginner to advanced Go developer.

## 🎯 Learning Path Overview

Each day focuses on specific concepts with:
- 📖 **Theory and explanations** in README.md
- 💻 **Runnable code examples** in separate .go files
- 🏃‍♂️ **Quick start scripts** to run all examples
- 🎯 **Practice exercises** with solutions
- 🔧 **Real-world applications** and best practices

## 📚 Tutorial Structure

### Week 1: Fundamentals
- **[Day 1: Go Basics & Tooling](day1_basics/)** - Setup, variables, functions
- **[Day 2: Structs, Methods & Interfaces](day2_structs_interfaces/)** - Object-oriented programming
- **[Day 3: Concurrency](day3_concurrency/)** - Goroutines and channels
- **[Day 4: REST API Part 1](day4_restapi1/)** - HTTP servers and routing
- **[Day 5: REST API Part 2](day5_restapi2/)** - Database integration

### Week 2: Intermediate Concepts
- **Day 6: Error Handling & Testing** - Robust error management
- **Day 7: File I/O & JSON** - Working with files and data formats
- **Day 8: Packages & Modules** - Code organization and dependencies
- **Day 9: Reflection & Generics** - Advanced type system features
- **Day 10: Performance & Profiling** - Optimization techniques

### Week 3: Advanced Topics
- **Day 11: Context & Cancellation** - Managing request lifecycles
- **Day 12: Middleware & Authentication** - Building secure APIs
- **Day 13: Database Patterns** - Advanced database operations
- **Day 14: Microservices** - Distributed system patterns
- **Day 15: Deployment & Docker** - Production deployment

## 🚀 Quick Start

### Run a specific day's tutorials:
```bash
cd day1_basics
./run.sh
```

### Run individual examples:
```bash
cd day1_basics
go run hello.go
go run types.go
```

### Test your knowledge:
```bash
cd day1_basics/solutions
go run exercise1.go
```

## 📋 Prerequisites

- Go 1.21 or later installed
- Basic command line knowledge
- Text editor or IDE (VS Code with Go extension recommended)

## 🛠 Setup

1. **Clone or download this repository**
2. **Verify Go installation:**
   ```bash
   go version
   ```
3. **Navigate to any day's folder and start learning!**

## 📖 How to Use These Tutorials

1. **Read the README.md** in each day's folder for theory
2. **Run the examples** using `./run.sh` or individual files
3. **Experiment** by modifying the code
4. **Complete the exercises** to reinforce learning
5. **Check solutions** if you get stuck
6. **Move to the next day** when comfortable

## 🎯 Learning Tips

- **Practice daily** - Consistency is key
- **Type the code** - Don't just read, write!
- **Experiment** - Modify examples to see what happens
- **Ask questions** - Use Go community resources
- **Build projects** - Apply what you learn

## 📚 Additional Resources

- [Official Go Documentation](https://golang.org/doc/)
- [Go by Example](https://gobyexample.com/)
- [Effective Go](https://golang.org/doc/effective_go.html)
- [Go Playground](https://play.golang.org/)

## 🤝 Contributing

Found an issue or want to improve a tutorial? Feel free to:
- Report bugs or unclear explanations
- Suggest additional examples
- Add more practice exercises
- Improve documentation

## 📅 Daily Schedule Suggestion

- **Morning (15-20 min)**: Read theory and understand concepts
- **Afternoon (20-30 min)**: Run examples and experiment
- **Evening (15-20 min)**: Complete exercises and review

**Total daily commitment: ~1 hour**

---

Happy coding! 🚀 Remember, the best way to learn Go is by writing Go code every day.
