package main

import "fmt"

// Basic struct definition
type User struct {
	ID   int
	Name string
	Age  int
}

// Struct with different field types
type Product struct {
	ID          int
	Name        string
	Price       float64
	InStock     bool
	Categories  []string
}

// Nested struct
type Address struct {
	Street  string
	City    string
	Country string
	ZipCode string
}

type Employee struct {
	ID      int
	Name    string
	Email   string
	Address Address // Nested struct
}

func main() {
	fmt.Println("=== Go Structs Demo ===")
	
	// 1. Basic struct creation and initialization
	fmt.Println("\n1. Basic Struct Creation:")
	
	// Method 1: Field by field
	var user1 User
	user1.ID = 1
	user1.Name = "Alice"
	user1.Age = 25
	fmt.Printf("User1: %+v\n", user1)
	
	// Method 2: Struct literal with field names
	user2 := User{
		ID:   2,
		Name: "Bob",
		Age:  30,
	}
	fmt.Printf("User2: %+v\n", user2)
	
	// Method 3: Struct literal without field names (order matters)
	user3 := User{3, "<PERSON>", 28}
	fmt.Printf("User3: %+v\n", user3)
	
	// 2. Working with different field types
	fmt.Println("\n2. Complex Struct with Various Types:")
	
	product := Product{
		ID:         101,
		Name:       "Laptop",
		Price:      999.99,
		InStock:    true,
		Categories: []string{"Electronics", "Computers"},
	}
	fmt.Printf("Product: %+v\n", product)
	
	// Accessing and modifying fields
	product.Price = 899.99
	product.Categories = append(product.Categories, "Sale")
	fmt.Printf("Updated Product: %+v\n", product)
	
	// 3. Nested structs
	fmt.Println("\n3. Nested Structs:")
	
	employee := Employee{
		ID:    1001,
		Name:  "John Doe",
		Email: "<EMAIL>",
		Address: Address{
			Street:  "123 Main St",
			City:    "New York",
			Country: "USA",
			ZipCode: "10001",
		},
	}
	fmt.Printf("Employee: %+v\n", employee)
	
	// Accessing nested fields
	fmt.Printf("Employee lives in: %s, %s\n", employee.Address.City, employee.Address.Country)
	
	// 4. Zero values
	fmt.Println("\n4. Zero Values:")
	
	var emptyUser User
	var emptyProduct Product
	fmt.Printf("Empty User: %+v\n", emptyUser)
	fmt.Printf("Empty Product: %+v\n", emptyProduct)
	
	// 5. Pointers to structs
	fmt.Println("\n5. Pointers to Structs:")
	
	userPtr := &user1
	fmt.Printf("User pointer: %p\n", userPtr)
	fmt.Printf("User via pointer: %+v\n", *userPtr)
	
	// Go automatically dereferences pointers for field access
	userPtr.Name = "Alice Updated"
	fmt.Printf("Updated user1: %+v\n", user1)
	
	// 6. Anonymous structs
	fmt.Println("\n6. Anonymous Structs:")
	
	config := struct {
		Host string
		Port int
		SSL  bool
	}{
		Host: "localhost",
		Port: 8080,
		SSL:  false,
	}
	fmt.Printf("Config: %+v\n", config)
}
