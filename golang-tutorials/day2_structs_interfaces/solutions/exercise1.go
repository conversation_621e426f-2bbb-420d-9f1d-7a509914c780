package main

import "fmt"

// Exercise 1 Solution: Create a Book struct with title, author, and pages. Add methods to display info.

type Book struct {
	Title  string
	Author string
	Pages  int
	ISBN   string
	Genre  string
}

// Method to display basic book information
func (b Book) DisplayInfo() string {
	return fmt.Sprintf("'%s' by %s (%d pages)", b.Title, b.Author, b.Pages)
}

// Method to display detailed book information
func (b Book) DisplayDetailedInfo() string {
	return fmt.Sprintf("Title: %s\nAuthor: %s\nPages: %d\nISBN: %s\nGenre: %s", 
		b.Title, b.Author, b.Pages, b.ISBN, b.Genre)
}

// Method to check if it's a long book
func (b Book) IsLongBook() bool {
	return b.Pages > 300
}

// Method to get reading time estimate (assuming 250 words per page, 200 words per minute)
func (b Book) EstimatedReadingTime() string {
	wordsPerPage := 250
	wordsPerMinute := 200
	totalWords := b.Pages * wordsPerPage
	minutes := totalWords / wordsPerMinute
	hours := minutes / 60
	remainingMinutes := minutes % 60
	
	if hours > 0 {
		return fmt.Sprintf("%d hours %d minutes", hours, remainingMinutes)
	}
	return fmt.Sprintf("%d minutes", minutes)
}

// Pointer receiver method to update book information
func (b *Book) UpdateISBN(newISBN string) {
	b.ISBN = newISBN
}

func main() {
	fmt.Println("=== Book Management System ===")
	
	// Create some books
	book1 := Book{
		Title:  "The Go Programming Language",
		Author: "Alan Donovan and Brian Kernighan",
		Pages:  380,
		ISBN:   "978-0134190440",
		Genre:  "Programming",
	}
	
	book2 := Book{
		Title:  "Clean Code",
		Author: "Robert C. Martin",
		Pages:  464,
		ISBN:   "978-0132350884",
		Genre:  "Programming",
	}
	
	book3 := Book{
		Title:  "The Hobbit",
		Author: "J.R.R. Tolkien",
		Pages:  310,
		ISBN:   "978-0547928227",
		Genre:  "Fantasy",
	}
	
	books := []Book{book1, book2, book3}
	
	// Display information for all books
	for i, book := range books {
		fmt.Printf("\n--- Book %d ---\n", i+1)
		fmt.Println(book.DisplayInfo())
		fmt.Printf("Long book: %t\n", book.IsLongBook())
		fmt.Printf("Estimated reading time: %s\n", book.EstimatedReadingTime())
		
		if book.Genre == "Programming" {
			fmt.Println("📚 This is a programming book!")
		}
	}
	
	// Demonstrate detailed info
	fmt.Println("\n--- Detailed Information ---")
	fmt.Println(book1.DisplayDetailedInfo())
	
	// Demonstrate pointer receiver method
	fmt.Println("\n--- Updating ISBN ---")
	fmt.Printf("Before: %s\n", book1.ISBN)
	book1.UpdateISBN("978-0134190441")
	fmt.Printf("After: %s\n", book1.ISBN)
}
