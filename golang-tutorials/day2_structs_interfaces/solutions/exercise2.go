package main

import (
	"fmt"
	"math"
)

// Exercise 2 Solution: Create a Shape interface with Area() method. Implement it for Rectangle and Circle.

// Shape interface defines what all shapes must implement
type Shape interface {
	Area() float64
	Perimeter() float64
	Name() string
}

// Rectangle struct
type Rectangle struct {
	Width  float64
	Height float64
}

// Circle struct
type Circle struct {
	Radius float64
}

// Triangle struct (bonus)
type Triangle struct {
	Base   float64
	Height float64
	SideA  float64
	SideB  float64
	SideC  float64
}

// Rectangle methods implementing Shape interface
func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

func (r Rectangle) Name() string {
	return "Rectangle"
}

// Additional Rectangle methods
func (r Rectangle) IsSquare() bool {
	return r.Width == r.Height
}

func (r Rectangle) Diagonal() float64 {
	return math.Sqrt(r.Width*r.Width + r.Height*r.Height)
}

// Circle methods implementing Shape interface
func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
	return 2 * math.Pi * c.Radius
}

func (c Circle) Name() string {
	return "Circle"
}

// Additional Circle methods
func (c Circle) Diameter() float64 {
	return 2 * c.Radius
}

func (c Circle) Circumference() float64 {
	return c.Perimeter() // Same as perimeter for a circle
}

// Triangle methods implementing Shape interface
func (t Triangle) Area() float64 {
	return 0.5 * t.Base * t.Height
}

func (t Triangle) Perimeter() float64 {
	return t.SideA + t.SideB + t.SideC
}

func (t Triangle) Name() string {
	return "Triangle"
}

// Additional Triangle methods
func (t Triangle) IsEquilateral() bool {
	return t.SideA == t.SideB && t.SideB == t.SideC
}

func (t Triangle) IsIsosceles() bool {
	return t.SideA == t.SideB || t.SideB == t.SideC || t.SideA == t.SideC
}

// Functions that work with the Shape interface

func printShapeInfo(s Shape) {
	fmt.Printf("%s:\n", s.Name())
	fmt.Printf("  Area: %.2f\n", s.Area())
	fmt.Printf("  Perimeter: %.2f\n", s.Perimeter())
}

func compareShapes(s1, s2 Shape) {
	fmt.Printf("\nComparing %s and %s:\n", s1.Name(), s2.Name())
	
	if s1.Area() > s2.Area() {
		fmt.Printf("  %s has larger area (%.2f vs %.2f)\n", s1.Name(), s1.Area(), s2.Area())
	} else if s1.Area() < s2.Area() {
		fmt.Printf("  %s has larger area (%.2f vs %.2f)\n", s2.Name(), s2.Area(), s1.Area())
	} else {
		fmt.Printf("  Both shapes have equal area (%.2f)\n", s1.Area())
	}
}

func calculateTotalArea(shapes []Shape) float64 {
	total := 0.0
	for _, shape := range shapes {
		total += shape.Area()
	}
	return total
}

func findLargestShape(shapes []Shape) Shape {
	if len(shapes) == 0 {
		return nil
	}
	
	largest := shapes[0]
	for _, shape := range shapes[1:] {
		if shape.Area() > largest.Area() {
			largest = shape
		}
	}
	return largest
}

func main() {
	fmt.Println("=== Shape Interface Demo ===")
	
	// Create different shapes
	rectangle := Rectangle{Width: 5, Height: 3}
	square := Rectangle{Width: 4, Height: 4}
	circle := Circle{Radius: 3}
	triangle := Triangle{
		Base:   6,
		Height: 4,
		SideA:  5,
		SideB:  5,
		SideC:  6,
	}
	
	// Store shapes in a slice using the interface
	shapes := []Shape{rectangle, square, circle, triangle}
	
	// Print information for all shapes
	fmt.Println("\n1. Shape Information:")
	for i, shape := range shapes {
		fmt.Printf("\nShape %d - ", i+1)
		printShapeInfo(shape)
	}
	
	// Demonstrate polymorphism
	fmt.Println("\n2. Polymorphism Demo:")
	for _, shape := range shapes {
		fmt.Printf("%s area: %.2f\n", shape.Name(), shape.Area())
	}
	
	// Compare shapes
	fmt.Println("\n3. Shape Comparisons:")
	compareShapes(rectangle, circle)
	compareShapes(square, triangle)
	
	// Calculate total area
	fmt.Println("\n4. Total Area Calculation:")
	total := calculateTotalArea(shapes)
	fmt.Printf("Total area of all shapes: %.2f\n", total)
	
	// Find largest shape
	fmt.Println("\n5. Largest Shape:")
	largest := findLargestShape(shapes)
	if largest != nil {
		fmt.Printf("Largest shape: %s with area %.2f\n", largest.Name(), largest.Area())
	}
	
	// Type-specific operations using type assertions
	fmt.Println("\n6. Type-Specific Operations:")
	for _, shape := range shapes {
		switch s := shape.(type) {
		case Rectangle:
			fmt.Printf("%s: ", s.Name())
			if s.IsSquare() {
				fmt.Printf("This is a square! ")
			}
			fmt.Printf("Diagonal: %.2f\n", s.Diagonal())
		case Circle:
			fmt.Printf("%s: Diameter: %.2f, Circumference: %.2f\n", 
				s.Name(), s.Diameter(), s.Circumference())
		case Triangle:
			fmt.Printf("%s: ", s.Name())
			if s.IsEquilateral() {
				fmt.Println("This is an equilateral triangle!")
			} else if s.IsIsosceles() {
				fmt.Println("This is an isosceles triangle!")
			} else {
				fmt.Println("This is a scalene triangle!")
			}
		}
	}
}
