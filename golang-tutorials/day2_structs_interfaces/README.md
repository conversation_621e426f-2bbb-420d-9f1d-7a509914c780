# Day 2: Structs, Methods, and Interfaces

## Goal
Learn how to use structs, methods, and interfaces in Go to build object-oriented programs.

---

## 🚀 Quick Start
Run all examples for this day:
```bash
./run.sh
```

Or run individual examples:
```bash
go run structs.go
go run methods.go
go run interfaces.go
go run advanced.go
```

---

## 1. Structs
📁 **File: `structs.go`**

Structs are typed collections of fields. They are the building blocks for data structures in Go.
- Struct definition and initialization
- Field access and modification
- Struct literals and zero values
- Nested structs

## 2. Methods
📁 **File: `methods.go`**

Methods are functions with a receiver argument that allow you to define behavior for your types.
- Value receivers vs pointer receivers
- Method sets
- Method chaining

## 3. Interfaces
📁 **File: `interfaces.go`**

Interfaces specify method signatures. Types implement interfaces implicitly.
- Interface definition and implementation
- Empty interface
- Type assertions and type switches
- Interface composition

## 4. Advanced Concepts
📁 **File: `advanced.go`**

Putting it all together with real-world examples:
- Building a simple employee management system
- Polymorphism with interfaces
- Error handling with custom types

---

## 🎯 Practice Exercises

1. **Exercise 1**: Create a `Book` struct with title, author, and pages. Add methods to display info.
2. **Exercise 2**: Create a `Shape` interface with `Area()` method. Implement it for Rectangle and Circle.
3. **Exercise 3**: Build a simple banking system with Account struct and Transaction interface.

**Solutions are in the `solutions/` folder**

---

## Summary
- You learned about structs, methods, and interfaces in Go.
- You practiced building object-oriented programs with Go's type system.
- Next: Concurrency with goroutines and channels.

