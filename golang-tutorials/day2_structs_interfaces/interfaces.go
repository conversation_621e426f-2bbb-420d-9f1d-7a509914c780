package main

import (
	"fmt"
	"math"
)

// 1. Basic interface definition
type Greeter interface {
	Greet() string
}

// 2. Interface with multiple methods
type Speaker interface {
	Speak() string
	GetLanguage() string
}

// 3. Shape interface for geometric calculations
type Shape interface {
	Area() float64
	Perimeter() float64
}

// 4. Writer interface (similar to io.Writer)
type Writer interface {
	Write(data string) error
}

// Implementing types

// User implements Greeter
type User struct {
	Name string
	Age  int
}

func (u User) Greet() string {
	return fmt.Sprintf("Hello, I'm %s and I'm %d years old", u.Name, u.Age)
}

// Robot implements both Greeter and Speaker
type Robot struct {
	Model    string
	Language string
}

func (r Robot) Greet() string {
	return fmt.Sprintf("BEEP BOOP. I am robot model %s", r.Model)
}

func (r Robot) Speak() string {
	return "PROCESSING... HELLO HUMAN"
}

func (r Robot) GetLanguage() string {
	return r.Language
}

// Rectangle implements Shape
type Rectangle struct {
	Width  float64
	Height float64
}

func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

// Circle implements Shape
type Circle struct {
	Radius float64
}

func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
	return 2 * math.Pi * c.Radius
}

// ConsoleWriter implements Writer
type ConsoleWriter struct{}

func (cw ConsoleWriter) Write(data string) error {
	fmt.Printf("[CONSOLE] %s\n", data)
	return nil
}

// FileWriter implements Writer
type FileWriter struct {
	Filename string
}

func (fw FileWriter) Write(data string) error {
	fmt.Printf("[FILE: %s] %s\n", fw.Filename, data)
	return nil
}

// Functions that work with interfaces

func greetSomeone(g Greeter) {
	fmt.Println(g.Greet())
}

func makeSpeak(s Speaker) {
	fmt.Printf("Speaking in %s: %s\n", s.GetLanguage(), s.Speak())
}

func printShapeInfo(s Shape) {
	fmt.Printf("Area: %.2f, Perimeter: %.2f\n", s.Area(), s.Perimeter())
}

func writeData(w Writer, data string) {
	err := w.Write(data)
	if err != nil {
		fmt.Printf("Error writing: %v\n", err)
	}
}

func main() {
	fmt.Println("=== Go Interfaces Demo ===")
	
	// 1. Basic interface usage
	fmt.Println("\n1. Basic Interface Usage:")
	
	user := User{Name: "Alice", Age: 25}
	robot := Robot{Model: "R2D2", Language: "Binary"}
	
	// Both User and Robot implement Greeter
	greetSomeone(user)
	greetSomeone(robot)
	
	// 2. Interface with multiple methods
	fmt.Println("\n2. Interface with Multiple Methods:")
	
	// Only Robot implements Speaker
	makeSpeak(robot)
	// makeSpeak(user) // This would cause a compile error
	
	// 3. Shape interface polymorphism
	fmt.Println("\n3. Shape Interface Polymorphism:")
	
	rectangle := Rectangle{Width: 5, Height: 3}
	circle := Circle{Radius: 4}
	
	shapes := []Shape{rectangle, circle}
	
	for i, shape := range shapes {
		fmt.Printf("Shape %d: ", i+1)
		printShapeInfo(shape)
	}
	
	// 4. Writer interface
	fmt.Println("\n4. Writer Interface:")
	
	console := ConsoleWriter{}
	file := FileWriter{Filename: "output.txt"}
	
	writers := []Writer{console, file}
	
	for _, writer := range writers {
		writeData(writer, "Hello, World!")
	}
	
	// 5. Empty interface (interface{})
	fmt.Println("\n5. Empty Interface:")
	
	var anything interface{}
	
	anything = 42
	fmt.Printf("anything = %v (type: %T)\n", anything, anything)
	
	anything = "Hello"
	fmt.Printf("anything = %v (type: %T)\n", anything, anything)
	
	anything = user
	fmt.Printf("anything = %v (type: %T)\n", anything, anything)
	
	// 6. Type assertions
	fmt.Println("\n6. Type Assertions:")
	
	var greeter Greeter = user
	
	// Type assertion
	if u, ok := greeter.(User); ok {
		fmt.Printf("Greeter is a User: %+v\n", u)
	}
	
	// Type assertion that would fail
	if _, ok := greeter.(Robot); !ok {
		fmt.Println("Greeter is not a Robot")
	}
	
	// 7. Type switches
	fmt.Println("\n7. Type Switches:")
	
	items := []interface{}{42, "hello", user, robot, 3.14}
	
	for i, item := range items {
		switch v := item.(type) {
		case int:
			fmt.Printf("Item %d: Integer %d\n", i, v)
		case string:
			fmt.Printf("Item %d: String '%s'\n", i, v)
		case User:
			fmt.Printf("Item %d: User %s\n", i, v.Name)
		case Robot:
			fmt.Printf("Item %d: Robot %s\n", i, v.Model)
		default:
			fmt.Printf("Item %d: Unknown type %T\n", i, v)
		}
	}
	
	// 8. Interface composition
	fmt.Println("\n8. Interface Composition:")
	
	// We can check if a type implements multiple interfaces
	var speaker Speaker = robot
	if greeterRobot, ok := speaker.(Greeter); ok {
		fmt.Printf("Robot can also greet: %s\n", greeterRobot.Greet())
	}
}
