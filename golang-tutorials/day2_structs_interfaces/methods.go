package main

import (
	"fmt"
	"strings"
)

// User struct for demonstrating methods
type User struct {
	ID       int
	Name     string
	Email    string
	Age      int
	IsActive bool
}

// BankAccount struct for demonstrating pointer receivers
type BankAccount struct {
	AccountNumber string
	Balance       float64
	Owner         string
}

// 1. Value receiver methods
func (u User) Greet() string {
	return fmt.Sprintf("Hello, I'm %s!", u.Name)
}

func (u User) GetInfo() string {
	status := "inactive"
	if u.IsActive {
		status = "active"
	}
	return fmt.Sprintf("User %d: %s (%s) - %s", u.ID, u.Name, u.Email, status)
}

func (u User) IsAdult() bool {
	return u.Age >= 18
}

// This method receives a copy of the struct, so changes won't persist
func (u User) TryToActivate() {
	u.IsActive = true // This won't change the original struct
	fmt.Printf("Inside TryToActivate: IsActive = %t\n", u.IsActive)
}

// 2. Pointer receiver methods
func (u *User) Activate() {
	u.IsActive = true // This will change the original struct
}

func (u *User) Deactivate() {
	u.IsActive = false
}

func (u *User) UpdateEmail(newEmail string) {
	if strings.Contains(newEmail, "@") {
		u.Email = newEmail
	}
}

func (u *User) HaveBirthday() {
	u.Age++
	fmt.Printf("%s is now %d years old!\n", u.Name, u.Age)
}

// 3. BankAccount methods demonstrating when to use pointer receivers
func (b BankAccount) GetBalance() float64 {
	return b.Balance
}

func (b BankAccount) GetAccountInfo() string {
	return fmt.Sprintf("Account %s (Owner: %s): $%.2f", b.AccountNumber, b.Owner, b.Balance)
}

func (b *BankAccount) Deposit(amount float64) {
	if amount > 0 {
		b.Balance += amount
		fmt.Printf("Deposited $%.2f. New balance: $%.2f\n", amount, b.Balance)
	}
}

func (b *BankAccount) Withdraw(amount float64) bool {
	if amount > 0 && amount <= b.Balance {
		b.Balance -= amount
		fmt.Printf("Withdrew $%.2f. New balance: $%.2f\n", amount, b.Balance)
		return true
	}
	fmt.Printf("Insufficient funds. Current balance: $%.2f\n", b.Balance)
	return false
}

// 4. Method chaining example
type Calculator struct {
	value float64
}

func (c *Calculator) Add(n float64) *Calculator {
	c.value += n
	return c
}

func (c *Calculator) Multiply(n float64) *Calculator {
	c.value *= n
	return c
}

func (c *Calculator) Subtract(n float64) *Calculator {
	c.value -= n
	return c
}

func (c Calculator) Result() float64 {
	return c.value
}

func main() {
	fmt.Println("=== Go Methods Demo ===")
	
	// 1. Value receiver methods
	fmt.Println("\n1. Value Receiver Methods:")
	
	user := User{
		ID:       1,
		Name:     "Alice",
		Email:    "<EMAIL>",
		Age:      25,
		IsActive: false,
	}
	
	fmt.Println(user.Greet())
	fmt.Println(user.GetInfo())
	fmt.Printf("Is adult: %t\n", user.IsAdult())
	
	// 2. Demonstrating value vs pointer receivers
	fmt.Println("\n2. Value vs Pointer Receivers:")
	
	fmt.Printf("Before TryToActivate: IsActive = %t\n", user.IsActive)
	user.TryToActivate()
	fmt.Printf("After TryToActivate: IsActive = %t\n", user.IsActive)
	
	fmt.Printf("Before Activate: IsActive = %t\n", user.IsActive)
	user.Activate() // Go automatically takes the address
	fmt.Printf("After Activate: IsActive = %t\n", user.IsActive)
	
	// 3. More pointer receiver methods
	fmt.Println("\n3. Pointer Receiver Methods:")
	
	user.UpdateEmail("<EMAIL>")
	fmt.Println(user.GetInfo())
	
	user.HaveBirthday()
	user.HaveBirthday()
	
	user.Deactivate()
	fmt.Println(user.GetInfo())
	
	// 4. BankAccount example
	fmt.Println("\n4. BankAccount Methods:")
	
	account := BankAccount{
		AccountNumber: "ACC-001",
		Balance:       1000.00,
		Owner:         "John Doe",
	}
	
	fmt.Println(account.GetAccountInfo())
	
	account.Deposit(250.50)
	account.Withdraw(100.00)
	account.Withdraw(2000.00) // Should fail
	
	fmt.Printf("Final balance: $%.2f\n", account.GetBalance())
	
	// 5. Method chaining
	fmt.Println("\n5. Method Chaining:")
	
	calc := &Calculator{value: 10}
	result := calc.Add(5).Multiply(2).Subtract(3).Result()
	fmt.Printf("((10 + 5) * 2) - 3 = %.2f\n", result)
	
	// 6. Methods on pointer vs value
	fmt.Println("\n6. Methods on Pointers vs Values:")
	
	userPtr := &user
	fmt.Println("Calling methods on pointer:")
	fmt.Println(userPtr.Greet())     // Works fine
	userPtr.Activate()               // Works fine
	fmt.Println(userPtr.GetInfo())
}
