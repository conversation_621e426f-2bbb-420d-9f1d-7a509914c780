package main

import (
	"fmt"
	"time"
)

// Employee Management System - Real-world example

// Employee interface defines what all employees can do
type Employee interface {
	GetID() int
	GetName() string
	GetSalary() float64
	GetRole() string
	Work() string
	CalculateBonus() float64
}

// Payable interface for things that can be paid
type Payable interface {
	ProcessPayment() string
}

// Base employee struct
type BaseEmployee struct {
	ID       int
	Name     string
	Salary   float64
	HireDate time.Time
}

// Developer struct
type Developer struct {
	BaseEmployee
	ProgrammingLanguages []string
	ProjectsCompleted    int
}

// Manager struct
type Manager struct {
	BaseEmployee
	TeamSize     int
	Department   string
	TeamMembers  []Employee
}

// Intern struct
type Intern struct {
	BaseEmployee
	Mentor   string
	Duration int // months
}

// Implementing Employee interface for Developer
func (d Developer) GetID() int {
	return d.ID
}

func (d Developer) GetName() string {
	return d.Name
}

func (d Developer) GetSalary() float64 {
	return d.Salary
}

func (d Developer) GetRole() string {
	return "Developer"
}

func (d Developer) Work() string {
	return fmt.Sprintf("%s is coding in %v", d.Name, d.ProgrammingLanguages)
}

func (d Developer) CalculateBonus() float64 {
	return d.Salary * 0.1 * float64(d.ProjectsCompleted)
}

// Implementing Payable interface for Developer
func (d Developer) ProcessPayment() string {
	total := d.Salary + d.CalculateBonus()
	return fmt.Sprintf("Processing payment of $%.2f for developer %s", total, d.Name)
}

// Implementing Employee interface for Manager
func (m Manager) GetID() int {
	return m.ID
}

func (m Manager) GetName() string {
	return m.Name
}

func (m Manager) GetSalary() float64 {
	return m.Salary
}

func (m Manager) GetRole() string {
	return "Manager"
}

func (m Manager) Work() string {
	return fmt.Sprintf("%s is managing %d team members in %s department", 
		m.Name, m.TeamSize, m.Department)
}

func (m Manager) CalculateBonus() float64 {
	return m.Salary * 0.15 * float64(m.TeamSize)
}

// Implementing Payable interface for Manager
func (m Manager) ProcessPayment() string {
	total := m.Salary + m.CalculateBonus()
	return fmt.Sprintf("Processing payment of $%.2f for manager %s", total, m.Name)
}

// Implementing Employee interface for Intern
func (i Intern) GetID() int {
	return i.ID
}

func (i Intern) GetName() string {
	return i.Name
}

func (i Intern) GetSalary() float64 {
	return i.Salary
}

func (i Intern) GetRole() string {
	return "Intern"
}

func (i Intern) Work() string {
	return fmt.Sprintf("%s is learning under mentor %s", i.Name, i.Mentor)
}

func (i Intern) CalculateBonus() float64 {
	// Interns get a small completion bonus
	return 500.0
}

// Implementing Payable interface for Intern
func (i Intern) ProcessPayment() string {
	total := i.Salary + i.CalculateBonus()
	return fmt.Sprintf("Processing payment of $%.2f for intern %s", total, i.Name)
}

// Company struct to manage employees
type Company struct {
	Name      string
	Employees []Employee
}

func (c *Company) AddEmployee(emp Employee) {
	c.Employees = append(c.Employees, emp)
	fmt.Printf("Added %s (%s) to %s\n", emp.GetName(), emp.GetRole(), c.Name)
}

func (c Company) ListEmployees() {
	fmt.Printf("\n=== %s Employee List ===\n", c.Name)
	for _, emp := range c.Employees {
		fmt.Printf("ID: %d, Name: %s, Role: %s, Salary: $%.2f\n", 
			emp.GetID(), emp.GetName(), emp.GetRole(), emp.GetSalary())
	}
}

func (c Company) ProcessAllPayments() {
	fmt.Printf("\n=== Processing Payments for %s ===\n", c.Name)
	for _, emp := range c.Employees {
		// Type assertion to check if employee is payable
		if payable, ok := emp.(Payable); ok {
			fmt.Println(payable.ProcessPayment())
		}
	}
}

func (c Company) GetTotalSalaryExpense() float64 {
	total := 0.0
	for _, emp := range c.Employees {
		total += emp.GetSalary() + emp.CalculateBonus()
	}
	return total
}

func (c Company) ShowWorkActivities() {
	fmt.Printf("\n=== Work Activities at %s ===\n", c.Name)
	for _, emp := range c.Employees {
		fmt.Println(emp.Work())
	}
}

func main() {
	fmt.Println("=== Advanced Go: Employee Management System ===")
	
	// Create company
	company := Company{Name: "TechCorp"}
	
	// Create employees
	dev1 := Developer{
		BaseEmployee: BaseEmployee{
			ID:       1,
			Name:     "Alice Johnson",
			Salary:   75000,
			HireDate: time.Now().AddDate(-2, 0, 0),
		},
		ProgrammingLanguages: []string{"Go", "Python", "JavaScript"},
		ProjectsCompleted:    5,
	}
	
	dev2 := Developer{
		BaseEmployee: BaseEmployee{
			ID:       2,
			Name:     "Bob Smith",
			Salary:   80000,
			HireDate: time.Now().AddDate(-3, 0, 0),
		},
		ProgrammingLanguages: []string{"Go", "Rust", "C++"},
		ProjectsCompleted:    7,
	}
	
	manager := Manager{
		BaseEmployee: BaseEmployee{
			ID:       3,
			Name:     "Carol Williams",
			Salary:   95000,
			HireDate: time.Now().AddDate(-5, 0, 0),
		},
		TeamSize:   8,
		Department: "Engineering",
	}
	
	intern := Intern{
		BaseEmployee: BaseEmployee{
			ID:       4,
			Name:     "David Brown",
			Salary:   2000,
			HireDate: time.Now().AddDate(0, -3, 0),
		},
		Mentor:   "Alice Johnson",
		Duration: 6,
	}
	
	// Add employees to company
	company.AddEmployee(dev1)
	company.AddEmployee(dev2)
	company.AddEmployee(manager)
	company.AddEmployee(intern)
	
	// Demonstrate polymorphism
	company.ListEmployees()
	company.ShowWorkActivities()
	company.ProcessAllPayments()
	
	// Calculate total expenses
	fmt.Printf("\nTotal salary expense: $%.2f\n", company.GetTotalSalaryExpense())
	
	// Demonstrate interface type checking
	fmt.Println("\n=== Interface Type Checking ===")
	for _, emp := range company.Employees {
		switch e := emp.(type) {
		case Developer:
			fmt.Printf("%s knows %d programming languages\n", 
				e.GetName(), len(e.ProgrammingLanguages))
		case Manager:
			fmt.Printf("%s manages %d people in %s\n", 
				e.GetName(), e.TeamSize, e.Department)
		case Intern:
			fmt.Printf("%s is mentored by %s for %d months\n", 
				e.GetName(), e.Mentor, e.Duration)
		}
	}
}
